package com.ysten.ugs.handler;

import com.ysten.ugs.exception.BusinessException;
import com.ysten.ugs.response.RestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@ControllerAdvice
public class UgsExceptionHandler
{
    @ResponseBody
    @ExceptionHandler(Throwable.class)
    public RestResponse handlerException(HttpServletRequest request, HandlerMethod method, Throwable ex)
    {
        RestResponse<?> resp;
        // 参数异常
        String className = method.getBeanType().getName();
        String methodName = method.getMethod().getName();
        
        if (ex instanceof IllegalArgumentException)
        {
            resp = RestResponse.illegal(ex.getMessage());
            log.info("IllegalArgumentException {}.{}:{}", className, methodName, resp);
        }
        else if (ex instanceof MaxUploadSizeExceededException)
        {
            resp = RestResponse.illegal("max filesize is 2M!");
            log.info("MaxUploadSizeExceededException {}.{}:{}", className, methodName, resp);
            
        }
        // 业务异常
        else if (ex instanceof BusinessException)
        {
            BusinessException be = (BusinessException)ex;
            resp = RestResponse.businessException(be.getCode(), ex.getMessage());
            log.info("BusinessException {}.{}:{}", className, methodName, resp);
        }
        // 其他
        else
        {
            resp = RestResponse.exception("调用接口报错." + StringUtils.abbreviate(ex.getMessage(), 30));
            log.error("exception {}.{}:{},{}", className, methodName, resp, ex);
            log.error("异常:", ex);
        }
        return resp;
    }
}
