package com.ysten.ugs.controller.old;

import com.ysten.ugs.controller.old.common.BaseController;
import com.ysten.ugs.response.UserLabelResponse;
import com.ysten.ugs.response.WebResult;
import com.ysten.ugs.service.label.LabelService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  <AUTHOR>
 *  @Date 2020/9/17 14:22
 *  @Description nucs请求
 */
@Slf4j
@RestController
@RequestMapping
@AllArgsConstructor
@Validated
@Api(value = "标签数据搜索", tags = {"标签数据搜索"})
public class LabelApiToNucsController extends BaseController {

    @Autowired
    private LabelService labelService;

    @RequestMapping(value = "/subsystem/findUserLabelList")
    public WebResult getUserLabelList(
            @RequestParam(value = "userLabelCode", defaultValue = "") String[] userLabelCode,
            @RequestParam(value = "userLabelName", defaultValue = "") String userLabelName,
            @RequestParam(value = "userLabelType", defaultValue = "") String userLabelType,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        log.info("ndms调用ugs分页查询标签list-start，接口名称[/subsystem/findUserLabelList],参数:userLabelCode={},userLabelName={},userLabelType={}",
                userLabelCode, userLabelName, userLabelType);
        if (pageNo < 1 || pageSize < 0) {
            return WebResult.error("参数非法pageNo=" + pageNo + ",pageSize=" + pageSize);
        }
        UserLabelResponse labelPageInfo = labelService.getLabelPageInfo(userLabelCode, userLabelName, userLabelType, pageNo, pageSize);
        return WebResult.success(labelPageInfo.getUserLabelList());
    }
}
