package com.ysten.ugs.controller.mq;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Configuration
@EnableKafka
public class KafkaFtpProducerConfig {

    @Value("${spring.kafka.ftp.producer.bootstrap.servers}")
    private String servers;

    @Value("${spring.kafka.ftp.producer.retries}")
    private String retries;

    @Value("${spring.kafka.ftp.producer.batch.size}")
    private String batchSize;

    @Value("${spring.kafka.ftp.producer.linger.ms}")
    private String lingerMs;

    @Value("${spring.kafka.ftp.producer.buffer.memory}")
    private String bufferMemory;

    @Value("${spring.kafka.ftp.producer.max.block.ms}")
    private String maxBlockMs;

    @Value("${spring.kafka.ftp.producer.acks}")
    private String acks;

    @Bean
    @ConditionalOnProperty(name = {"spring.kafka.ftp.isSync"}, havingValue = "true")
    public ProducerFactory<String, String> ftpProducerFactory() {
        return new DefaultKafkaProducerFactory<>(ftpProducerConfigs());
    }

    /**
     * Kafka配置信息
     *
     * @param
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2022/7/5
     */
    @Bean
    @ConditionalOnProperty(name = {"spring.kafka.ftp.isSync"}, havingValue = "true")
    public Map<String, Object> ftpProducerConfigs() {
        Map<String, Object> props = new HashMap<>(10);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.RETRIES_CONFIG, retries);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSize);
        props.put(ProducerConfig.LINGER_MS_CONFIG, lingerMs);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemory);
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, maxBlockMs);
        props.put(ProducerConfig.ACKS_CONFIG, acks);
        return props;
    }

    @Bean
    @ConditionalOnProperty(name = {"spring.kafka.ftp.isSync"}, havingValue = "true")
    public KafkaTemplate<String, String> ftpKafkaTemplate() {
        return new KafkaTemplate<>(ftpProducerFactory());
    }
}
