package com.ysten.ugs.controller.old.common;


import com.ysten.ugs.constants.UgsConstants;
import com.ysten.ugs.entity.base.ApiConfig;
import com.ysten.ugs.service.ndms.ApiConfigService;
import com.ysten.ugs.util.AuthCheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;

/**
 * 基础Controller层，抽象方法，增加参数校验
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseController {

    @Autowired
    private ApiConfigService apiConfigService;

    protected void validateData(String partnerCode, HttpServletRequest request) {
        Assert.isTrue(StringUtils.isNotEmpty(partnerCode), "partnerCode不能为空!");
        ApiConfig abiConfig = apiConfigService.findApiConfigById(partnerCode);

        Assert.isTrue(abiConfig != null, partnerCode+"错误,请确认是否正确!");

        if (StringUtils.equalsIgnoreCase(abiConfig.getStatus(), UgsConstants.STOP)) {
            throw new IllegalArgumentException("此"+partnerCode +"已经停止合作!");
        }
        if (StringUtils.equalsIgnoreCase(abiConfig.getStatus(), UgsConstants.NOTCHECK)) {
            log.info("此"+partnerCode+"放通不需要校验");
            return;
        }
        AuthCheckUtils.validateData(partnerCode, abiConfig.getPartnerKey(), request);

    }



}
