package com.ysten.ugs.job.sourceData.increment;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ysten.ugs.annotation.JobAccessLog;
import com.ysten.ugs.constants.CacheConstant;
import com.ysten.ugs.constants.CommonConstant;
import com.ysten.ugs.constants.SystemConstants;
import com.ysten.ugs.constants.UgsConstants;
import com.ysten.ugs.dto.order.UserOrderDTO;
import com.ysten.ugs.enums.SystemDataDescEnum;
import com.ysten.ugs.service.datasource.VasOrderService;
import com.ysten.ugs.service.operation.transformation.IncrementOrderDataBuildService;
import com.ysten.ugs.service.quartz.IQuartzJobHistoryService;
import com.ysten.ugs.service.realtime.UserOrderService;
import com.ysten.ugs.service.statistics.StatisticsSyncDataService;
import com.ysten.ugs.service.system.ISysDictService;
import com.ysten.ugs.service.user.UserService;
import com.ysten.ugs.util.LocalDateTimeUtils;
import com.ysten.ugs.util.StringUtil;
import com.ysten.ugs.util.sys.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 *  <AUTHOR>
 *  @Date 2022/10/20 9:58
 *  @Description 同步订单
 */
@Component
@Slf4j
@DisallowConcurrentExecution
@RestController
public class IncrementOrderDataMethodJob implements Job {

    @Autowired
    private VasOrderService vasOrderService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private UserOrderService userOrderService;

    @Autowired
    private IncrementOrderDataBuildService incrementOrderDataBuildService;

    @Autowired
    private StatisticsSyncDataService statisticsSyncDataService;

    @Autowired
    private ISysDictService sysDictService;






    /**
     * 同步增量订单数据(服务启动后 70秒执行，之后每单个任务「执行完」后 60秒执行一次)
     * <AUTHOR>
     * @date 2022/9/9
     * @return void
     **/
    @JobAccessLog(jobName = "同步增量订单数据", hintMsgSwitch = CommonConstant.SWITCH_ON)
    @Override
    @GetMapping("incrementUserOrder")
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try{
            Object obj = redisUtil.get(CacheConstant.ORDER_DATA_EXECUTION_TIME + this.getClass().getName());
            if (obj != null && SystemConstants.ZERO.equals(obj)) {
                log.info("****请勿频繁同步增量订单数据JOB****");
                return;
            }
            redisUtil.set(CacheConstant.ORDER_DATA_EXECUTION_TIME + this.getClass().getName(), SystemConstants.ZERO, UgsConstants.JOB_AUTO_EXPIRE_TIME);
            log.info("***************开始同步增量订单数据***************");
            String type = "INIT_NO";
            Integer initFlag = (Integer) redisUtil.get(CacheConstant.USER_INIT_COMPLETE_FLAG);
            if(!SystemConstants.ONE.equals(initFlag)){
                log.info("***************用户数据未初始化完成！***************");
                return;
            }
            LocalDateTime startDate ;

            Integer orderNum = userOrderService.getUserOrderTotal();
            if(SystemConstants.ZERO.equals(orderNum)){
                UserOrderDTO orderDTO = vasOrderService.getOldestOrderDate();
                log.info("获取订单最早时间{}", orderDTO);
                LocalDateTime baseDate = LocalDateTimeUtils.parseLocalDateTime("2001-01-01 00:00:00");
                if (orderDTO != null) {
                    log.info("获取订单最早时间1{}", orderDTO.getUpdateDate());
                    startDate = orderDTO.getUpdateDate();
                    if (startDate.isBefore(baseDate)) {
                        startDate = baseDate;
                    }
                } else {
                    startDate = baseDate;
                }
                type = "INIT";
            }else{
                startDate = userService.getLatestUserOrderDate();
            }
            //获取订单截止时间
            LocalDateTime endDate = userService.getLatestUserBusinessDate();
            Object lastModifyTimeObj = redisUtil.get(CacheConstant.USER_DATA_LAST_MODIFY_TIME);
            if (lastModifyTimeObj != null) {
                endDate = LocalDateTimeUtils.parseLocalDateTime(lastModifyTimeObj.toString());
            }else{
                if(endDate == null){
                    log.error("用户表数据为空！");
                    return;
                }
            }
            log.info("同步订单数据的截至时间为：" + endDate);
            Integer count = this.handleIncrementOrder(startDate,endDate,type);
            this.statisticsDataNum(count);
            log.info("***************完成同步增量订单数据***************");
        }catch (Exception e){
            log.info("JOB 同步增量订单执行异常！e ={}",e);
            redisUtil.set(CacheConstant.ORDER_DATA_EXECUTION_TIME + this.getClass().getName(),SystemConstants.ONE, UgsConstants.SYS_NOTIFY_DINGDING_CACHE_TIME);
            throw new JobExecutionException("同步增量订单执行失败，失败原因" + e.getMessage());
        }
        redisUtil.remove(CacheConstant.ORDER_DATA_EXECUTION_TIME + this.getClass().getName());
    }

    private Integer handleIncrementOrder(LocalDateTime startDate, LocalDateTime endDate, String type){
        /**
         * 订单同步时休眠的时间
         * 因为订单表的数据量比较大，导致同步的时候CK调度容易使得ZK磁盘占满，从而导致ZK挂掉，减慢一下同步的速率
         */
        String ORDER_SLEEP = sysDictService.queryDictTextByKey(UgsConstants.SLEEP_TIME, UgsConstants.SLEEP_TIME_ORDER);
        Long sleepLong = 8000L;
        if(StringUtils.isNotBlank(ORDER_SLEEP)){
            sleepLong = Long.parseLong(ORDER_SLEEP);
        }
        List<LocalDateTime> localDateTimeList = Lists.newArrayList();
        localDateTimeList.add(startDate);
        LocalDateTime newEndDate = startDate;
        boolean flag = true;
        while(flag){
            LocalDateTime lastEndDate;
            if("INIT".equals(type)){
                lastEndDate = newEndDate.plusDays(SystemConstants.ONE);
            }else{
                lastEndDate = newEndDate.plusSeconds(SystemConstants.THIRTY);
            }
            if(endDate.isBefore(newEndDate)){
                flag = false;
            }else{
                newEndDate = lastEndDate;
                localDateTimeList.add(newEndDate);
            }
        }
        int totalNum = 0;
        LocalDateTime maxDate = null;
        if(CollectionUtils.isNotEmpty(localDateTimeList)){
            log.info("********处理的订单变更数据量**********");
            LocalDateTime lastDate = endDate;
            List<UserOrderDTO> totalOrderLists = Lists.newArrayList();
            int batch = 50000;
            try {
                for(int ii=0;ii<localDateTimeList.size();ii++){
                    if(ii < localDateTimeList.size()-1){
                        LocalDateTime sDate = localDateTimeList.get(ii);
                        LocalDateTime eDate = localDateTimeList.get(ii+1);
                        if(eDate.isAfter(endDate)){
                            eDate = endDate;
                        }
                        List<UserOrderDTO> orders = vasOrderService.getOrderInfo(sDate, eDate);
                        Map<String, String> checkRepeatMap = Maps.newHashMap();
                        log.info("********查询订单时间区间{}**********【{}】",sDate+"=="+eDate,CollectionUtils.isNotEmpty(orders)?orders.size():0);
                        if(CollectionUtils.isNotEmpty(orders)){
                            totalOrderLists.addAll(orders);
                            for(UserOrderDTO orderDTO : orders){
                                if(StringUtil.isNotBlank(orderDTO.getCode())){
                                    checkRepeatMap.put(orderDTO.getCode(),orderDTO.getCode());
                                }
                            }
                        }

                        //orderTableSeparate配置改为从字典表中获取
                        Boolean orderTableSeparate = false;
                        String newOrderTableSeparateStr = sysDictService.queryDictTextByKey("orderDataMethodJob", "orderTableSeparate");
                        if(StringUtil.isNotBlank(newOrderTableSeparateStr) && "true".equals(newOrderTableSeparateStr)){
                            orderTableSeparate = true;
                        } else {
                            orderTableSeparate = false;
                        }

                        if (orderTableSeparate) {
                            List<UserOrderDTO> pendList = vasOrderService.getOrderInfoByPend(sDate, eDate);
                            log.info("********查询订单时间区间 for pend{}**********【{}】", sDate + "==" + eDate, CollectionUtils.isNotEmpty(orders) ? orders.size() : 0);
                            if (CollectionUtils.isNotEmpty(pendList)) {
                                for (UserOrderDTO orderDTO : pendList) {
                                    if (StringUtil.isNotBlank(orderDTO.getCode()) && !checkRepeatMap.containsKey(orderDTO.getCode())) {
                                        totalOrderLists.add(orderDTO);
                                    }
                                }
                            }
                        }

                        if(totalOrderLists.size() > SystemConstants.CLICK_SAVE_SIZE || lastDate.equals(eDate)) {
                            LocalDateTime nowMaxDate = totalOrderLists.stream().map(UserOrderDTO::getUpdateDate).max(LocalDateTime::compareTo).get();
                            List<List<UserOrderDTO>> parts = Lists.partition(totalOrderLists, batch);
                            for(List<UserOrderDTO> totalOrders : parts){
                                incrementOrderDataBuildService.handleIncrementOrderInfo(totalOrders,type);
                                Thread.sleep(sleepLong);
                                totalNum = totalNum + totalOrders.size();
                                log.info("********处理截至到{}的订单数据量【{}】-【{}】**********",eDate,totalOrders.size(),totalNum);
                            }
                            totalOrderLists.clear();
                            if(null == maxDate || maxDate.isBefore(nowMaxDate)){
                                maxDate = nowMaxDate;
                            }
                        } else {
                            maxDate = eDate;
                        }
                    }
                }
                log.info("同步订单数据的最后更新时间【{}】", maxDate);
            }catch (Exception e){
                e.printStackTrace();
            }
            log.info("********订单变更数据处理完毕**********");
        }
        return totalNum;
    }

    private void statisticsDataNum(Integer total) {
        try{
            Map<String,Integer> tableCount = Maps.newHashMap();
            tableCount.put("user_order",total);
            statisticsSyncDataService.statisticsDataNum(tableCount, "com.ysten.ugs.job.sourceData.increment.IncrementOrderDataMethodJob", SystemDataDescEnum.NVAS.getCode());
        }catch (Exception e) {
            log.info("统计同步用户订单数据异常");
        }
    }
}
