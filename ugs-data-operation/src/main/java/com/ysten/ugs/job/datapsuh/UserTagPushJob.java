package com.ysten.ugs.job.datapsuh;

import com.ysten.ugs.annotation.JobAccessLog;
import com.ysten.ugs.context.UserTagPushDictContext;
import com.ysten.ugs.dto.user.UserTagFileDTO;
import com.ysten.ugs.manager.common.UserTagManager;
import com.ysten.ugs.manager.upload.FileUploadManager;
import com.ysten.ugs.mapper.ugs.label.LabelMapper;
import com.ysten.ugs.mapper.ugs.system.SysDictMapper;
import com.ysten.ugs.util.FileUtils;
import com.ysten.ugs.vo.sys.DictModel;
import com.ysten.ugs.entity.label.Label;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * UserTagPushJob 用户标签推送任务
 *
 * <AUTHOR>
 * @version 2024/11/12
 * @since 2024-11-12
 */
@Component
@Slf4j
@DisallowConcurrentExecution
@RestController
public class UserTagPushJob implements Job {


    @Resource
    private SysDictMapper sysDictMapper;

    @Resource
    private LabelMapper labelMapper;

    @Resource
    private UserTagManager userTagManager;

    @Autowired
    private Map<String,FileUploadManager> fileUploadManagerMap;


    @JobAccessLog(jobName = "IOP用户标签推送")
    @Override
    @GetMapping("iopTagPush")
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            //-----1. 查询字典表 对应信息
            Map<String, String> dictMap = Optional.ofNullable(sysDictMapper.queryDictItemsByCode("ugs_user_tag"))
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(DictModel::getText, DictModel::getValue));
            // 转对象存储
            UserTagPushDictContext userTagPushDictContext = UserTagPushDictContext.fromMap(dictMap);
            // code list
            List<String> codeList = Stream.of(userTagPushDictContext.getLabelCodes().split(","))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(codeList)) {
                log.error("字典表 labelCodes 未配置");
                return;
            }

            //-----2.查mysql  查询code 获取返回结果 转map
            Map<String, String> map = Optional.ofNullable(labelMapper.findByCodeList(codeList))
                .orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(Label::getCode, Label::getName));

            FileUploadManager fileUploadManager = fileUploadManagerMap.get(userTagPushDictContext.getManager());

            // 初始化  ftp 链接 参数
            fileUploadManager.init(dictMap, userTagPushDictContext.getIssue());
            // 校验文件是否存在
            if (fileUploadManager.fileExistsOnSftp(userTagPushDictContext.getOkFileName())) {
                log.info("用户标签推送任务已完成");
                return;
            }

            //-----3.查询ck 并 写入到本地文件
            map.forEach((key, value) -> userTagManager.queryAndWriteData2YiDong(key, value, userTagPushDictContext, dictMap));
            if (userTagPushDictContext.getCount() == 0) {
                log.error("用户标签查询数量为0.请检查对应labelCode 输入是否正确！");
                return;
            }
            // 循环结束 最后一个文件 加到文件结果集合中
            userTagPushDictContext.getFileList().add(new UserTagFileDTO(userTagPushDictContext.getCount(),
                userTagPushDictContext.getDataFileName(),userTagPushDictContext.getLocalFilePath(), 0L));

            // 推送数据文件
            fileUploadManager.uploadDataFile(userTagPushDictContext.getFileList());

            // 本地生成校验文件  并 上传
            String verifyFilePath = userTagPushDictContext.getLocalFilePath() + "/" + userTagPushDictContext.getOkFileName();
            userTagManager.generateVerifyFile(userTagPushDictContext.getFileList(), verifyFilePath);
            fileUploadManager.uploadVerifyFile(verifyFilePath);

            // 删除 本地 文件 verifyFile,dataFile nio
            userTagPushDictContext.getFileList().forEach(userTagFileDTO -> {
                try {
                    FileUtils.deleteFile(userTagFileDTO.getDataFile());
                    FileUtils.deleteFile(userTagFileDTO.getCheckFile());
                    FileUtils.deleteFile(verifyFilePath);
                } catch (IOException ex) {
                    log.error("删除文件异常", ex);
                }
            });
            log.info("用户标签推送任务执行完成");
        } catch (Exception e) {
            log.error("用户标签推送任务执行异常,请检查", e);
            throw new JobExecutionException(e);
        }
    }
}
