package com.ysten.ugs.job.sourceData.increment;

import com.ysten.ugs.annotation.JobAccessLog;
import com.ysten.ugs.constants.CacheConstant;
import com.ysten.ugs.constants.CommonConstant;
import com.ysten.ugs.constants.SystemConstants;
import com.ysten.ugs.constants.UgsConstants;
import com.ysten.ugs.service.basicEnum.UserBasicEnumService;
import com.ysten.ugs.util.sys.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@DisallowConcurrentExecution
public class IncrementBasicEnumDataMethodJob implements Job {

    @Autowired
    private UserBasicEnumService userBasicEnumService;

    @Autowired
    private RedisUtil redisUtil;



    /**
     * 同步基础枚举数据
     * @param jobExecutionContext
     * @throws JobExecutionException
     */
    @JobAccessLog(jobName = "同步基础枚举数据", hintMsgSwitch = CommonConstant.SWITCH_ON)
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try{
            Object obj = redisUtil.get(CacheConstant.BASIC_ENUM_LAST_EXECUTION_TIME + this.getClass().getName());
            if (obj != null && SystemConstants.ZERO.equals(obj)) {
                log.info("****请勿频繁同步基础枚举JOB****");
                return;
            }
            redisUtil.set(CacheConstant.BASIC_ENUM_LAST_EXECUTION_TIME + this.getClass().getName(), SystemConstants.ZERO, UgsConstants.JOB_AUTO_EXPIRE_TIME);
            log.info("***************开始同步基础枚举数据***************");
            userBasicEnumService.handleUserBasicEnumService("com.ysten.ugs.job.sourceData.increment.IncrementBasicEnumDataMethodJob");
            log.info("***************完成同步基础枚举数据***************");
        }catch (Exception e){
            log.info("JOB 同步增量节目单执行异常！e ={}",e);
            redisUtil.set(CacheConstant.BASIC_ENUM_LAST_EXECUTION_TIME + this.getClass().getName(),SystemConstants.ONE, UgsConstants.SYS_NOTIFY_DINGDING_CACHE_TIME);
            throw new JobExecutionException("同步基础枚举数据执行失败，失败原因" + e.getMessage());
        }
        redisUtil.remove(CacheConstant.BASIC_ENUM_LAST_EXECUTION_TIME + this.getClass().getName());
    }
}
