package com.ysten.ugs.job.train;

import com.ysten.ugs.annotation.JobAccessLog;
import com.ysten.ugs.constants.CacheConstant;
import com.ysten.ugs.constants.CommonConstant;
import com.ysten.ugs.constants.SystemConstants;
import com.ysten.ugs.constants.UgsConstants;
import com.ysten.ugs.service.train.UserPreferencesTrainService;
import com.ysten.ugs.service.train.UserSimilarTempService;
import com.ysten.ugs.util.sys.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@DisallowConcurrentExecution
public class UserSimilarTempDataJob implements Job {

    @Autowired
    private UserSimilarTempService userSimilarTempService;

    @Autowired
    private RedisUtil redisUtil;

    @JobAccessLog(jobName = "用户相似人群数据", hintMsgSwitch = CommonConstant.SWITCH_ON)
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try{
            Object obj = redisUtil.get(CacheConstant.USER_SIMILAR_TEMP_DATA_EXECUTION_TIME + this.getClass().getName());
            if (obj != null && SystemConstants.ZERO.equals(obj)) {
                log.info("****请勿频繁调用用户相似人群数据处理JOB****");
                return;
            }
            redisUtil.set(CacheConstant.USER_SIMILAR_TEMP_DATA_EXECUTION_TIME + this.getClass().getName(), SystemConstants.ZERO, UgsConstants.JOB_AUTO_EXPIRE_TIME);
            log.info("***************开始调用用户相似人群数据处理JOB***************");
            userSimilarTempService.handleUserSimilarTempData();
            log.info("***************完成调用用户相似人群数据处理JOB***************");
        }catch (Exception e){
            log.info("JOB 调用用户相似人群数据处理JOB执行异常！e ={}",e);
            redisUtil.set(CacheConstant.USER_SIMILAR_TEMP_DATA_EXECUTION_TIME + this.getClass().getName(),SystemConstants.ONE, UgsConstants.SYS_NOTIFY_DINGDING_CACHE_TIME);
            throw new JobExecutionException("用户相似人群数据处理JOB执行失败，失败原因" + e.getMessage());
        }
        redisUtil.remove(CacheConstant.USER_SIMILAR_TEMP_DATA_EXECUTION_TIME + this.getClass().getName());
    }
}
