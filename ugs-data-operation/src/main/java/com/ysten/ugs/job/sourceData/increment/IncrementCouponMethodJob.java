package com.ysten.ugs.job.sourceData.increment;

import com.ysten.ugs.annotation.JobAccessLog;
import com.ysten.ugs.constants.CacheConstant;
import com.ysten.ugs.constants.CommonConstant;
import com.ysten.ugs.constants.SystemConstants;
import com.ysten.ugs.constants.UgsConstants;
import com.ysten.ugs.dao.user.UserCouponCodeDao;
import com.ysten.ugs.dto.coupon.VasCouponCodeDTO;
import com.ysten.ugs.manager.sourcedata.vas.VasCouponManager;
import com.ysten.ugs.util.sys.RedisUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * IncrementCouponMethodJob 增量同步优惠券数据
 *
 * <AUTHOR>
 * @version 2025/2/26
 * @since 2025-02-26
 */
@Component
@Slf4j
@DisallowConcurrentExecution
public class IncrementCouponMethodJob implements Job {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserCouponCodeDao userCouponCodeDao;

    @Autowired
    private VasCouponManager vasCouponManager;

    @JobAccessLog(jobName = "增量同步VAS优惠券数据", hintMsgSwitch = CommonConstant.SWITCH_ON)
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        Object obj = redisUtil.get(CacheConstant.COUPON_DATA_EXECUTION_TIME + this.getClass().getName());
        if (SystemConstants.ZERO.equals(obj)) {
            log.info("****请勿频繁同步优惠卷信息JOB****");
            return;
        }
        redisUtil.set(CacheConstant.COUPON_DATA_EXECUTION_TIME + this.getClass().getName(), SystemConstants.ZERO, UgsConstants.JOB_AUTO_EXPIRE_TIME);

        log.info("增量同步VAS优惠券数据 start");
        // 查询ck 已同步数据的最新更新时间 没有同步时 默认 2020-01-01
        String maxUpdateTime = userCouponCodeDao.getMaxUpdateTime();
        long count = 0L;
        int size = 5000;
        int offset = 0;
        while(true) {
            List<VasCouponCodeDTO> vasCouponCodeList = vasCouponManager.findVasCouponCodeList(maxUpdateTime, offset, size);

            if (CollectionUtils.isEmpty(vasCouponCodeList)) {
                log.info("查询为空，结束数据同步");
                break;
            }
            // 同步数据到ck
            userCouponCodeDao.saveBatch(vasCouponCodeList);
            // 统计总数
            count += vasCouponCodeList.size();
            log.info("增量同步优惠券数据,已同步数据条数:[{}]", count);
            if (vasCouponCodeList.size() < size) {
                log.info("查询数据小于{}，结束数据同步",size);
                break;
            }
            // 增加偏移量
            offset += size;
        }
        log.info("增量同步VAS优惠券数据 end,总共同步数据条数:[{}]", count);
    }
}
