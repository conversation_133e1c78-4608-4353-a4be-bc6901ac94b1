package com.ysten.ugs.job.sourceData.increment;

import com.google.common.collect.Maps;
import com.ysten.ugs.annotation.JobAccessLog;
import com.ysten.ugs.constants.CacheConstant;
import com.ysten.ugs.constants.CommonConstant;
import com.ysten.ugs.constants.SystemConstants;
import com.ysten.ugs.constants.UgsConstants;
import com.ysten.ugs.enums.SystemDataDescEnum;
import com.ysten.ugs.service.operation.transformation.IncrementCosCpContentBuildService;
import com.ysten.ugs.service.operation.transformation.IncrementCosProgramsetBuildService;
import com.ysten.ugs.service.program.ProgramSetService;
import com.ysten.ugs.service.program.ViewProgramService;
import com.ysten.ugs.service.statistics.StatisticsSyncDataService;
import com.ysten.ugs.util.LocalDateTimeUtils;
import com.ysten.ugs.util.sys.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 *  <AUTHOR>
 *  @Date 2022/10/20 9:51
 *  @Description 同步节目集信息
 */
@Component
@Slf4j
@DisallowConcurrentExecution
public class IncrementProgramSetDataMethodJob implements Job {

    @Autowired
    private IncrementCosProgramsetBuildService incrementCosProgramsetBuildService;

    @Autowired
    private IncrementCosCpContentBuildService incrementCosCpContentBuildService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StatisticsSyncDataService statisticsSyncDataService;


    /**
     * 同步增量节目集数据
     * @param jobExecutionContext
     * @throws JobExecutionException
     */
    @JobAccessLog(jobName = "同步增量节目集数据", hintMsgSwitch = CommonConstant.SWITCH_ON)
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            Object obj = redisUtil.get(CacheConstant.PROGRAM_SET_DATA_EXECUTION_TIME + this.getClass().getName());
            if (obj != null && SystemConstants.ZERO.equals(obj)) {
                log.info("****请勿频繁同步增量节目集JOB****");
                return;
            }
            redisUtil.set(CacheConstant.PROGRAM_SET_DATA_EXECUTION_TIME + this.getClass().getName(), SystemConstants.ZERO, UgsConstants.JOB_AUTO_EXPIRE_TIME);
            log.info("***************开始同步增量节目集数据***************");
            Integer count1 = incrementCosProgramsetBuildService.handleIncrementCosProgramsetInfo();
            Integer count2 = incrementCosCpContentBuildService.handleIncrementCpContent();
            incrementCosProgramsetBuildService.createProgramsetView();
            this.statisticsDataNum(count1 + count2);
            log.info("***************完成同步增量节目集数据***************");
        }catch (Exception e){
            log.info("JOB 同步增量节目集执行异常！e ={}",e);
            redisUtil.set(CacheConstant.PROGRAM_SET_DATA_EXECUTION_TIME + this.getClass().getName(),SystemConstants.ONE, UgsConstants.SYS_NOTIFY_DINGDING_CACHE_TIME);
            throw new JobExecutionException("同步增量节目集执行失败，失败原因" + e.getMessage());
        }
        redisUtil.remove(CacheConstant.PROGRAM_SET_DATA_EXECUTION_TIME + this.getClass().getName());
    }

    private void statisticsDataNum(Integer total) {
        try{
            Map<String,Integer> tableCount = Maps.newHashMap();
            tableCount.put("view_program",total);
            statisticsSyncDataService.statisticsDataNum(tableCount, "com.ysten.ugs.job.sourceData.increment.IncrementProgramSetDataMethodJob", SystemDataDescEnum.NCOS.getCode());
        }catch (Exception e) {
            log.info("统计同步节目集数据异常");
        }
    }


}
