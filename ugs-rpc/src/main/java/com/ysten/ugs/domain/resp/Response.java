package com.ysten.ugs.domain.resp;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Response<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    private static final String CODE_SUCCESS = "000";

    /**
     * 参数异常
     */
    private static final String CODE_ILLEGAL = "340";

    /**
     * 系统异常
     */
    private static final String CODE_SYSTEM_ERROR = "ugs-999";
    private static final String SYSTEM_MESSAGE = "系统异常！";

    protected String resultCode;
    protected String resultMessage;
    protected T data;

    public Response(String resultCode, String resultMessage) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
    }

    public Response(String resultCode, String resultMessage, T data) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
        this.data = data;
    }

    public static Response businessException(String code, String message) {
        return new Response(code, message);
    }

    public static Response illegal(String message) {
        return new Response(CODE_ILLEGAL, message);
    }

    public static Response systemException() {
        return new Response(CODE_SYSTEM_ERROR, SYSTEM_MESSAGE);
    }

    public static Response success() {
        return new Response(CODE_SUCCESS, "操作成功");
    }

    public static Response success(Object data) {
        return new Response(CODE_SUCCESS, "操作成功", data);
    }

    public boolean checkSuccess() {
        if (CODE_SUCCESS.equals(this.resultCode)) {
            return true;
        }

        return false;
    }
}
