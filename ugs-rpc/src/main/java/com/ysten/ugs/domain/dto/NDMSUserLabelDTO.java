package com.ysten.ugs.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * ndms调用ugs查询标签列表返回类型（对应：原先ndms调用nucs的接口）
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NDMSUserLabelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private long total;

    private List<NDMSUserLabelDTO> userLabelList;
}
