package com.ysten.ugs.domain.dto;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ResponseDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    private static final String CODE_SUCCESS = "000";

    /**
     * 参数异常
     */
    private static final String CODE_ILLEGAL = "340";

    /**
     * 系统异常
     */
    private static final String CODE_SYSTEM_ERROR = "ugs-999";

    protected String resultCode;
    protected String resultMessage;
    protected T data;

    public ResponseDTO(String resultCode, String resultMessage) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
    }

    public ResponseDTO(String resultCode, String resultMessage, T data) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
        this.data = data;
    }

    public static ResponseDTO businessException(String code, String message) {
        return new ResponseDTO(code, message);
    }

    public static ResponseDTO illegal(String message) {
        return new ResponseDTO(CODE_ILLEGAL, "参数校验失败！" + message);
    }

    public static ResponseDTO success() {
        return new ResponseDTO(CODE_SUCCESS, "操作成功");
    }

    public static ResponseDTO success(Object data) {
        return new ResponseDTO(CODE_SUCCESS, "操作成功", data);
    }

    public boolean isSuccess(){
        return "000".equals(resultCode) || "BSS-000".equalsIgnoreCase(resultCode) || "0".equals(resultCode);

    }
}
