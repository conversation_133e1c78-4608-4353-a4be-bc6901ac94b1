package com.ysten.ugs.rpc.label;

import java.util.List;

import com.ysten.ugs.domain.resp.Response;
import com.ysten.ugs.domain.bo.NDMSProductLabelBO;
import com.ysten.ugs.domain.bo.NDMSUserLabelBO;
import com.ysten.ugs.domain.bo.UserLabelReportBO;
import com.ysten.ugs.domain.bo.UserQuery;
import com.ysten.ugs.domain.dto.LabelDTO;

/**
 * 标签rpc接口
 *
 * <AUTHOR>
 * @date 2019/10/18
 */
public interface ILabelRpcService {

    /**
     * 用户标签查询
     */
    Response<List<LabelDTO>> queryUserLabels(UserQuery userQuery);

    /**
     * 批量上报用户标签
     * 老的实现方式处理List<UserReportBO> userReportBOList和List<LabelReportBO> reportLabelBOList这两个字段
     * 新的实现方式不再接收这两个字段的值
     */
    Response reportUserLabels(UserLabelReportBO labelReportDataBO);

    /**
     * ndms标签树
     * @return
     */
    Response ndmsFindLabelTree(String partnerCode);

    /**
     * 根据用户id查询标签信息
     * @param ndmsProductLabelBO
     * @return
     */
    Response ndmsGetTreeByUid(NDMSProductLabelBO ndmsProductLabelBO);

    /**
     * 根据条件分页查询标签（ndms调用ugs）
     * @param ndmsUserLabelBO
     * @return
     */
    Response getLabelPageInfo(NDMSUserLabelBO ndmsUserLabelBO);
}
