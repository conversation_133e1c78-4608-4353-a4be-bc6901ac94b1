package com.ysten.ugs.consumer;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.ysten.ugs.config.KafkaOffsetTools;
import com.ysten.ugs.constants.SystemConstants;
import com.ysten.ugs.dto.device.UserDeviceDTO;
import com.ysten.ugs.entity.label.Label;
import com.ysten.ugs.service.label.LabelService;
import com.ysten.ugs.service.operation.transformation.IncrementDeviceDataBuildService;
import com.ysten.ugs.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @ClassName ConsumerUserIncrementDataHandle
 * @Deacription 处理ucs用户增量数据
 * <AUTHOR>
 * @Date 2020/5/8 17:26
 **/
@Component
@Slf4j
public class ConsumerDeviceIncrementDataHandle {

    @Autowired
    private IncrementDeviceDataBuildService incrementDeviceDataBuildService;

    @Autowired
    private LabelService labelService;

    @Autowired
    private UserService userService;

    /**
     * 批量消费增量设备信息
     *
     * @param
     */
    public void consumerBatch(List<ConsumerRecord<String, byte[]>> recordList) {
        log.info("*****开始批量消费增量设备信息*****");
        long startDate = System.currentTimeMillis();
        try {
            if (CollectionUtils.isNotEmpty(recordList)) {
                recordList.forEach((record) -> {
                    List<UserDeviceDTO> deviceList = JSONArray.parseArray(new String(record.value()), UserDeviceDTO.class);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    incrementDeviceDataBuildService.handleIncrementDeviceInfo(deviceList);
                });
                this.handleChangeLabelCount();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("*****结束批量消费增量设备信息*****消耗时间：{}", System.currentTimeMillis() - startDate);
    }

    /**
     * 异步处理设备类事实标签
     *
     * @return void
     * <AUTHOR>
     * @date 2021/1/27
     **/
    @Async
    protected void handleChangeLabelCount() {
        log.info("*********开始处理增量设备事实标签数量统计*********");
        List<Label> labels = labelService.getLabelListByTable(Lists.newArrayList("user_device"));
        if (CollectionUtils.isNotEmpty(labels)) {
            labelService.changeLabelStatisticsCount(labels);
        }
        log.info("*********结束处理增量设备事实标签数量统计*********");
    }
}
