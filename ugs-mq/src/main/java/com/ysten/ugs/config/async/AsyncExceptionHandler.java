package com.ysten.ugs.config.async;

import java.lang.reflect.Method;

import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Async异步异常处理
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019/6/2 18:58
 */
@Slf4j
@Component
public class AsyncExceptionHandler implements AsyncUncaughtExceptionHandler {

    /**
     * Handle the given uncaught exception thrown from an asynchronous method.
     *
     * @param ex
     *            the exception thrown from the asynchronous method
     * @param method
     *            the asynchronous method
     * @param params
     *            the parameters used to invoked the method
     */
    @Override
    public void handleUncaughtException(Throwable ex, Method method, Object... params) {
        log.error("Method:{} asyncException:{}", method.getName(), ex.getMessage());
    }
}
