package com.ysten.ugs;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Date 2020/7/15 14:06
 * @Description 启动
 */
@ServletComponentScan(basePackages = {"com.ysten.ugs"})
@MapperScan(basePackages = {"com.ysten.ugs.mapper"})
@Configuration
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class UgsMqApplication {

    public static void main(String[] args) {
        SpringApplication.run(UgsMqApplication.class, args);
    }
}
